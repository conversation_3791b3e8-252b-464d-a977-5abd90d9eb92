#!/usr/bin/env python3
"""
BTC价格演示脚本
展示如何使用CCXT获取加密货币价格
"""

import asyncio
import ccxt.async_support as ccxt
from datetime import datetime
import json

async def demo_btc_price():
    """演示获取BTC价格"""
    
    print("🚀 BTC价格获取演示")
    print("=" * 50)
    print()
    
    # 支持的交易所
    exchanges_config = {
        'binance': ccxt.binance(),
        'kraken': ccxt.kraken(),
        'huobi': ccxt.huobi(),
    }
    
    print("📊 支持的交易所:")
    for i, exchange_name in enumerate(exchanges_config.keys(), 1):
        print(f"  {i}. {exchange_name.title()}")
    print()
    
    # 测试每个交易所的BTC价格
    for exchange_name, exchange in exchanges_config.items():
        print(f"💰 获取 {exchange_name.title()} 的BTC价格:")
        print("-" * 30)
        
        try:
            # 设置超时和速率限制
            exchange.timeout = 10000
            exchange.enableRateLimit = True
            
            # 获取BTC/USDT价格 (对于Kraken使用BTC/USD)
            symbol = "BTC/USDT" if exchange_name != "kraken" else "BTC/USD"
            
            ticker = await exchange.fetch_ticker(symbol)
            
            print(f"交易对: {symbol}")
            print(f"当前价格: ${ticker['last']:,.2f}")
            if ticker['percentage']:
                print(f"24h变化: {ticker['percentage']:+.2f}%")
            if ticker['high']:
                print(f"24h最高: ${ticker['high']:,.2f}")
            if ticker['low']:
                print(f"24h最低: ${ticker['low']:,.2f}")
            if ticker['baseVolume']:
                print(f"24h成交量: {ticker['baseVolume']:,.2f} BTC")
            print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ 获取 {exchange_name} 价格失败: {e}")
        
        finally:
            await exchange.close()
        
        print()

async def demo_crypto_prices():
    """演示获取多种加密货币价格"""
    
    print("💎 多种加密货币价格演示")
    print("=" * 50)
    
    # 使用Binance获取多种加密货币价格
    exchange = ccxt.binance()
    exchange.timeout = 10000
    exchange.enableRateLimit = True
    
    # 要查询的加密货币
    symbols = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
    
    try:
        print("📈 Binance 热门加密货币价格:")
        print("-" * 40)
        
        for symbol in symbols:
            try:
                ticker = await exchange.fetch_ticker(symbol)
                change_str = f"{ticker['percentage']:+.2f}%" if ticker['percentage'] else "N/A"
                print(f"{symbol:<10} ${ticker['last']:>10,.2f} ({change_str})")
            except Exception as e:
                print(f"{symbol:<10} 获取失败: {e}")
        
        print()
        
        # 获取市场概览
        print("🏆 交易量排行榜 (USDT交易对):")
        print("-" * 40)
        
        tickers = await exchange.fetch_tickers()
        
        # 筛选USDT交易对并按交易量排序
        usdt_pairs = [
            (symbol, ticker) for symbol, ticker in tickers.items()
            if 'USDT' in symbol and ticker.get('quoteVolume')
        ]
        
        # 按24h交易量排序
        sorted_pairs = sorted(usdt_pairs, key=lambda x: x[1]['quoteVolume'], reverse=True)
        
        for i, (symbol, ticker) in enumerate(sorted_pairs[:10], 1):
            change_str = f"{ticker['percentage']:+.2f}%" if ticker['percentage'] else "N/A"
            volume_str = f"{ticker['quoteVolume']:,.0f}" if ticker['quoteVolume'] else "N/A"
            print(f"{i:2d}. {symbol:<12} ${ticker['last']:>10,.4f} ({change_str}) Vol: ${volume_str}")
        
    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
    
    finally:
        await exchange.close()
    
    print()

async def demo_ohlcv_data():
    """演示获取K线数据"""
    
    print("📊 K线数据演示")
    print("=" * 50)
    
    exchange = ccxt.binance()
    exchange.timeout = 10000
    exchange.enableRateLimit = True
    
    try:
        symbol = "BTC/USDT"
        timeframe = "1h"
        limit = 10
        
        print(f"📈 {symbol} 最近{limit}根{timeframe}K线:")
        print("-" * 60)
        print("时间              开盘价    最高价    最低价    收盘价    成交量")
        print("-" * 60)
        
        ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        
        for candle in ohlcv:
            timestamp = datetime.fromtimestamp(candle[0] / 1000)
            time_str = timestamp.strftime('%m-%d %H:%M')
            open_price = candle[1]
            high_price = candle[2]
            low_price = candle[3]
            close_price = candle[4]
            volume = candle[5]
            
            print(f"{time_str} {open_price:>8.0f} {high_price:>8.0f} {low_price:>8.0f} {close_price:>8.0f} {volume:>10.2f}")
        
    except Exception as e:
        print(f"❌ 获取K线数据失败: {e}")
    
    finally:
        await exchange.close()
    
    print()

async def demo_exchange_info():
    """演示获取交易所信息"""
    
    print("🏢 交易所信息演示")
    print("=" * 50)
    
    exchange = ccxt.binance()
    exchange.timeout = 10000
    
    try:
        print("📋 Binance 交易所信息:")
        print("-" * 30)
        
        # 加载市场信息
        markets = await exchange.load_markets()
        
        # 统计信息
        total_markets = len(markets)
        btc_pairs = [symbol for symbol in markets.keys() if symbol.startswith('BTC/')]
        eth_pairs = [symbol for symbol in markets.keys() if symbol.startswith('ETH/')]
        usdt_pairs = [symbol for symbol in markets.keys() if symbol.endswith('/USDT')]
        
        print(f"总交易对数量: {total_markets}")
        print(f"BTC交易对: {len(btc_pairs)}")
        print(f"ETH交易对: {len(eth_pairs)}")
        print(f"USDT交易对: {len(usdt_pairs)}")
        
        print(f"\n支持的功能:")
        features = exchange.has
        important_features = ['fetchTicker', 'fetchTickers', 'fetchOHLCV', 'fetchOrderBook', 'fetchTrades']
        
        for feature in important_features:
            status = "✅" if features.get(feature, False) else "❌"
            print(f"  {status} {feature}")
        
        print(f"\n一些BTC交易对示例:")
        for i, symbol in enumerate(btc_pairs[:5], 1):
            print(f"  {i}. {symbol}")
        
    except Exception as e:
        print(f"❌ 获取交易所信息失败: {e}")
    
    finally:
        await exchange.close()
    
    print()

async def main():
    """主函数"""
    print("🎯 加密货币价格获取演示")
    print("基于CCXT库，支持多个主要交易所")
    print("=" * 60)
    print()
    
    try:
        await demo_btc_price()
        await demo_crypto_prices()
        await demo_ohlcv_data()
        await demo_exchange_info()
        
        print("🎉 演示完成!")
        print("=" * 60)
        print()
        print("💡 接下来你可以:")
        print("• 运行 'py -3.11 btc_price_mcp_server.py' 启动MCP服务器")
        print("• 在Claude Desktop中配置并使用这个服务器")
        print("• 扩展服务器功能，添加更多交易所和功能")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
