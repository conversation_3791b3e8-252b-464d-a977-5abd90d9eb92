#!/usr/bin/env python3
"""
BTC RSI表格演示
模拟5分钟RSI数据并用彩色表格显示
"""

import numpy as np
from datetime import datetime, timedelta
from typing import List
import random

# ANSI颜色代码
class Colors:
    RED = '\033[91m'      # 红色 (RSI < 25)
    GREEN = '\033[92m'    # 绿色 (RSI > 75)
    YELLOW = '\033[93m'   # 黄色 (25 <= RSI <= 75)
    BLUE = '\033[94m'     # 蓝色
    PURPLE = '\033[95m'   # 紫色
    CYAN = '\033[96m'     # 青色
    WHITE = '\033[97m'    # 白色
    BOLD = '\033[1m'      # 粗体
    UNDERLINE = '\033[4m' # 下划线
    END = '\033[0m'       # 结束颜色

def calculate_rsi(prices: List[float], period: int = 14) -> float:
    """计算RSI"""
    if len(prices) < period + 1:
        return None
    
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])
    
    for i in range(period, len(gains)):
        avg_gain = (avg_gain * (period - 1) + gains[i]) / period
        avg_loss = (avg_loss * (period - 1) + losses[i]) / period
    
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return round(rsi, 2)

def format_rsi_value(rsi: float) -> str:
    """根据RSI值返回带颜色的格式化字符串"""
    if rsi is None:
        return f"{Colors.WHITE}--{Colors.END}"
    
    rsi_str = f"{rsi:6.2f}"
    
    if rsi < 25:
        return f"{Colors.RED}{Colors.BOLD}{rsi_str}{Colors.END}"  # 红色粗体
    elif rsi > 75:
        return f"{Colors.GREEN}{Colors.BOLD}{rsi_str}{Colors.END}"  # 绿色粗体
    else:
        return f"{Colors.YELLOW}{rsi_str}{Colors.END}"  # 黄色

def get_rsi_signal(rsi: float) -> str:
    """获取RSI信号描述"""
    if rsi is None:
        return "无数据"
    
    if rsi < 25:
        return f"{Colors.RED}{Colors.BOLD}强烈超卖 🔴{Colors.END}"
    elif rsi < 30:
        return f"{Colors.RED}超卖 🟠{Colors.END}"
    elif rsi > 75:
        return f"{Colors.GREEN}{Colors.BOLD}强烈超买 🟢{Colors.END}"
    elif rsi > 70:
        return f"{Colors.GREEN}超买 🟡{Colors.END}"
    elif 45 <= rsi <= 55:
        return f"{Colors.CYAN}中性 ⚪{Colors.END}"
    else:
        return f"{Colors.YELLOW}正常 🔵{Colors.END}"

def generate_mock_btc_data(periods: int = 50) -> List[dict]:
    """生成模拟BTC 5分钟数据"""
    
    base_price = 95000
    current_time = datetime.now()
    data = []
    
    # 生成一些有趣的RSI模式
    price_changes = []
    
    # 创建一些超卖和超买的情况
    for i in range(periods):
        if i < 10:
            # 开始时正常波动
            change = random.uniform(-0.005, 0.005)
        elif i < 20:
            # 模拟下跌趋势 (可能导致超卖)
            change = random.uniform(-0.015, 0.005)
        elif i < 30:
            # 模拟反弹
            change = random.uniform(-0.005, 0.020)
        elif i < 40:
            # 模拟强势上涨 (可能导致超买)
            change = random.uniform(-0.002, 0.015)
        else:
            # 最近的正常波动
            change = random.uniform(-0.008, 0.008)
        
        price_changes.append(change)
    
    # 生成价格数据
    current_price = base_price
    for i in range(periods):
        timestamp = current_time - timedelta(minutes=5 * (periods - i - 1))
        
        current_price *= (1 + price_changes[i])
        
        # 添加一些随机的高低点
        high = current_price * (1 + random.uniform(0, 0.003))
        low = current_price * (1 - random.uniform(0, 0.003))
        open_price = current_price * (1 + random.uniform(-0.001, 0.001))
        volume = random.uniform(50, 200)
        
        data.append({
            'datetime': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': current_price,
            'volume': volume
        })
    
    return data

def print_header():
    """打印表格头部"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}{'='*90}{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}🚀 BTC 5分钟RSI监控表格 (演示数据){Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}{'='*90}{Colors.END}")
    print(f"{Colors.WHITE}更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Colors.END}")
    print(f"{Colors.WHITE}数据来源: 模拟 BTC/USDT 5分钟K线{Colors.END}")
    print(f"{Colors.RED}🔴 RSI < 25: 强烈超卖{Colors.END} | {Colors.GREEN}🟢 RSI > 75: 强烈超买{Colors.END} | {Colors.YELLOW}🟡 25-75: 正常区间{Colors.END}")
    print()

def print_rsi_table(data: List[dict]):
    """打印RSI表格"""
    
    # 计算所有RSI值
    close_prices = [candle['close'] for candle in data]
    rsi_14_values = []
    rsi_7_values = []
    rsi_21_values = []
    
    for i in range(len(close_prices)):
        # RSI(14)
        if i >= 14:
            rsi_14 = calculate_rsi(close_prices[i-14:i+1], 14)
        else:
            rsi_14 = None
        rsi_14_values.append(rsi_14)
        
        # RSI(7)
        if i >= 7:
            rsi_7 = calculate_rsi(close_prices[i-7:i+1], 7)
        else:
            rsi_7 = None
        rsi_7_values.append(rsi_7)
        
        # RSI(21)
        if i >= 21:
            rsi_21 = calculate_rsi(close_prices[i-21:i+1], 21)
        else:
            rsi_21 = None
        rsi_21_values.append(rsi_21)
    
    # 表格头部
    print(f"{Colors.BLUE}{Colors.BOLD}{'时间':<8} {'价格':<10} {'RSI(7)':<10} {'RSI(14)':<10} {'RSI(21)':<10} {'信号':<20} {'成交量':<8}{Colors.END}")
    print(f"{Colors.BLUE}{'-'*85}{Colors.END}")
    
    # 显示最近25条数据
    recent_count = min(25, len(data))
    start_idx = len(data) - recent_count
    
    for i in range(start_idx, len(data)):
        candle = data[i]
        rsi_7 = rsi_7_values[i]
        rsi_14 = rsi_14_values[i]
        rsi_21 = rsi_21_values[i]
        
        time_str = candle['datetime'].strftime('%H:%M')
        price_str = f"${candle['close']:,.0f}"
        volume_str = f"{candle['volume']:.0f}"
        
        rsi_7_colored = format_rsi_value(rsi_7)
        rsi_14_colored = format_rsi_value(rsi_14)
        rsi_21_colored = format_rsi_value(rsi_21)
        signal = get_rsi_signal(rsi_14)
        
        # 高亮当前行（最新数据）
        if i == len(data) - 1:
            prefix = f"{Colors.CYAN}► "
            suffix = f" ◄{Colors.END}"
        else:
            prefix = "  "
            suffix = ""
        
        print(f"{prefix}{time_str:<8} {price_str:<10} {rsi_7_colored:<17} {rsi_14_colored:<17} {rsi_21_colored:<17} {signal:<35} {volume_str:<8}{suffix}")

def print_summary(data: List[dict], rsi_values: List[float]):
    """打印摘要信息"""
    
    current_candle = data[-1]
    current_price = current_candle['close']
    current_rsi = rsi_values[-1] if rsi_values else None
    
    print(f"\n{Colors.CYAN}{Colors.BOLD}📊 当前状态摘要{Colors.END}")
    print(f"{Colors.BLUE}{'-'*50}{Colors.END}")
    
    # 当前价格和RSI
    print(f"💰 当前价格: {Colors.WHITE}{Colors.BOLD}${current_price:,.2f}{Colors.END}")
    print(f"📈 当前RSI(14): {format_rsi_value(current_rsi)}")
    print(f"🎯 信号: {get_rsi_signal(current_rsi)}")
    
    # 价格变化
    if len(data) >= 2:
        prev_price = data[-2]['close']
        price_change = current_price - prev_price
        price_change_pct = (price_change / prev_price) * 100
        
        if price_change > 0:
            change_color = Colors.GREEN
            change_symbol = "⬆️"
        else:
            change_color = Colors.RED
            change_symbol = "⬇️"
        
        print(f"📊 5分钟变化: {change_color}${price_change:+,.2f} ({price_change_pct:+.2f}%) {change_symbol}{Colors.END}")
    
    # RSI统计
    valid_rsi = [rsi for rsi in rsi_values if rsi is not None]
    if valid_rsi:
        rsi_min = min(valid_rsi)
        rsi_max = max(valid_rsi)
        rsi_avg = np.mean(valid_rsi)
        
        print(f"\n📊 RSI(14)统计 (最近{len(valid_rsi)}个周期):")
        print(f"   最低: {format_rsi_value(rsi_min)}")
        print(f"   最高: {format_rsi_value(rsi_max)}")
        print(f"   平均: {format_rsi_value(rsi_avg)}")
        
        # 计算超买超卖次数
        strong_oversold = sum(1 for rsi in valid_rsi if rsi < 25)
        oversold = sum(1 for rsi in valid_rsi if 25 <= rsi < 30)
        normal = sum(1 for rsi in valid_rsi if 30 <= rsi <= 70)
        overbought = sum(1 for rsi in valid_rsi if 70 < rsi <= 75)
        strong_overbought = sum(1 for rsi in valid_rsi if rsi > 75)
        
        print(f"\n🔍 RSI分布统计:")
        print(f"   {Colors.RED}{Colors.BOLD}强烈超卖 (<25): {strong_oversold}{Colors.END}")
        print(f"   {Colors.RED}超卖 (25-30): {oversold}{Colors.END}")
        print(f"   {Colors.YELLOW}正常 (30-70): {normal}{Colors.END}")
        print(f"   {Colors.GREEN}超买 (70-75): {overbought}{Colors.END}")
        print(f"   {Colors.GREEN}{Colors.BOLD}强烈超买 (>75): {strong_overbought}{Colors.END}")

def print_trading_suggestions(current_rsi: float):
    """打印交易建议"""
    
    print(f"\n{Colors.PURPLE}{Colors.BOLD}💡 交易建议 (仅供参考){Colors.END}")
    print(f"{Colors.BLUE}{'-'*40}{Colors.END}")
    
    if current_rsi is None:
        print(f"{Colors.WHITE}数据不足，无法给出建议{Colors.END}")
        return
    
    if current_rsi < 25:
        print(f"{Colors.RED}🔴 强烈超卖区域{Colors.END}")
        print(f"   • 考虑逢低买入")
        print(f"   • 等待反弹信号")
        print(f"   • 设置止损保护")
    elif current_rsi < 30:
        print(f"{Colors.RED}🟠 超卖区域{Colors.END}")
        print(f"   • 可能的买入机会")
        print(f"   • 观察是否有反转信号")
    elif current_rsi > 75:
        print(f"{Colors.GREEN}🟢 强烈超买区域{Colors.END}")
        print(f"   • 考虑获利了结")
        print(f"   • 警惕回调风险")
        print(f"   • 可考虑减仓")
    elif current_rsi > 70:
        print(f"{Colors.GREEN}🟡 超买区域{Colors.END}")
        print(f"   • 谨慎追高")
        print(f"   • 关注回调信号")
    else:
        print(f"{Colors.YELLOW}🔵 正常区域{Colors.END}")
        print(f"   • 趋势跟随策略")
        print(f"   • 等待明确信号")
        print(f"   • 保持观望")
    
    print(f"\n{Colors.RED}⚠️  风险提示: 以上建议仅供参考，投资有风险，决策需谨慎！{Colors.END}")

def main():
    """主函数"""
    
    print(f"{Colors.BOLD}{Colors.CYAN}🚀 BTC RSI 5分钟监控表格演示{Colors.END}")
    
    # 生成模拟数据
    print(f"{Colors.YELLOW}正在生成模拟数据...{Colors.END}")
    data = generate_mock_btc_data(50)
    
    # 计算RSI
    close_prices = [candle['close'] for candle in data]
    rsi_values = []
    
    for i in range(len(close_prices)):
        if i >= 14:
            rsi = calculate_rsi(close_prices[i-14:i+1], 14)
        else:
            rsi = None
        rsi_values.append(rsi)
    
    # 显示表格
    print_header()
    print_rsi_table(data)
    
    # 显示摘要
    print_summary(data, rsi_values)
    
    # 显示交易建议
    current_rsi = rsi_values[-1] if rsi_values else None
    print_trading_suggestions(current_rsi)
    
    print(f"\n{Colors.CYAN}{'='*90}{Colors.END}")
    print(f"{Colors.CYAN}演示完成！实际使用时将连接真实交易所数据。{Colors.END}")

if __name__ == "__main__":
    main()
