#!/usr/bin/env python3
"""
简单的MCP客户端示例
连接到MCP服务器并测试各种功能
"""

import asyncio
import subprocess
import sys
import os
from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_client():
    """测试MCP客户端功能"""

    # 启动服务器进程
    server_params = StdioServerParameters(
        command=sys.executable,  # 使用当前Python解释器
        args=[os.path.abspath("mcp_server_example.py")]  # 使用绝对路径
    )

    print("🚀 启动MCP客户端测试...")
    print("=" * 50)

    try:
        async with AsyncExitStack() as stack:
            # 连接到服务器
            streams = await stack.enter_async_context(stdio_client(server_params))
            read_stream, write_stream = streams

            # 创建客户端会话
            session = await stack.enter_async_context(ClientSession(read_stream, write_stream))

            # 初始化连接
            await session.initialize()
        
        print("✅ 成功连接到MCP服务器!")
        print()
        
        # 测试1: 列出可用工具
        print("📋 测试1: 列出可用工具")
        print("-" * 30)
        tools_result = await session.list_tools()
        print(f"发现 {len(tools_result.tools)} 个工具:")
        for tool in tools_result.tools:
            print(f"  • {tool.name}: {tool.description}")
        print()
        
        # 测试2: 列出可用资源
        print("📚 测试2: 列出可用资源")
        print("-" * 30)
        resources_result = await session.list_resources()
        print(f"发现 {len(resources_result.resources)} 个资源:")
        for resource in resources_result.resources:
            print(f"  • {resource.name} ({resource.uri})")
            print(f"    描述: {resource.description}")
        print()
        
        # 测试3: 读取资源
        print("📖 测试3: 读取资源")
        print("-" * 30)
        try:
            # 读取笔记资源
            note_result = await session.read_resource("note://note1")
            print(f"读取笔记1: {note_result.contents[0].text}")
            
            # 读取待办事项资源
            todos_result = await session.read_resource("todos://all")
            print(f"待办事项列表:")
            print(todos_result.contents[0].text)
        except Exception as e:
            print(f"读取资源时出错: {e}")
        print()
        
        # 测试4: 调用工具 - 计算器
        print("🧮 测试4: 使用计算器工具")
        print("-" * 30)
        try:
            calc_result = await session.call_tool("calculate", {"expression": "10 + 5 * 2"})
            print(f"计算结果: {calc_result.content[0].text}")
        except Exception as e:
            print(f"计算时出错: {e}")
        print()
        
        # 测试5: 调用工具 - 添加笔记
        print("📝 测试5: 添加新笔记")
        print("-" * 30)
        try:
            add_note_result = await session.call_tool("add_note", {
                "note_id": "test_note",
                "content": "这是通过MCP客户端添加的测试笔记"
            })
            print(f"添加笔记结果: {add_note_result.content[0].text}")
        except Exception as e:
            print(f"添加笔记时出错: {e}")
        print()
        
        # 测试6: 调用工具 - 获取刚添加的笔记
        print("🔍 测试6: 获取刚添加的笔记")
        print("-" * 30)
        try:
            get_note_result = await session.call_tool("get_note", {"note_id": "test_note"})
            print(f"获取笔记结果: {get_note_result.content[0].text}")
        except Exception as e:
            print(f"获取笔记时出错: {e}")
        print()
        
        # 测试7: 调用工具 - 添加待办事项
        print("✅ 测试7: 添加待办事项")
        print("-" * 30)
        try:
            add_todo_result = await session.call_tool("add_todo", {
                "task": "通过MCP客户端测试完成"
            })
            print(f"添加待办事项结果: {add_todo_result.content[0].text}")
        except Exception as e:
            print(f"添加待办事项时出错: {e}")
        print()
        
        # 测试8: 调用工具 - 完成待办事项
        print("🎯 测试8: 完成待办事项")
        print("-" * 30)
        try:
            complete_todo_result = await session.call_tool("complete_todo", {"todo_id": 1})
            print(f"完成待办事项结果: {complete_todo_result.content[0].text}")
        except Exception as e:
            print(f"完成待办事项时出错: {e}")
        print()
        
        # 测试9: 再次读取资源查看变化
        print("🔄 测试9: 查看更新后的资源")
        print("-" * 30)
        try:
            # 重新读取待办事项资源
            updated_todos_result = await session.read_resource("todos://all")
            print("更新后的待办事项列表:")
            print(updated_todos_result.contents[0].text)
        except Exception as e:
            print(f"读取更新后的资源时出错: {e}")
        print()
        
        print("🎉 所有测试完成!")
        print("=" * 50)

async def main():
    """主函数"""
    try:
        await test_mcp_client()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
