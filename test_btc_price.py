#!/usr/bin/env python3
"""
BTC价格MCP服务器测试脚本
直接测试服务器功能
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们的服务器
from btc_price_mcp_server import server

async def test_btc_price_server():
    """直接测试BTC价格服务器功能"""
    
    print("🧪 测试BTC价格MCP服务器功能")
    print("=" * 60)
    
    # 测试1: 列出工具
    print("🔧 测试1: 列出可用工具")
    print("-" * 40)
    try:
        tools = await server.list_tools()
        print(f"发现 {len(tools)} 个工具:")
        for i, tool in enumerate(tools, 1):
            print(f"  {i}. {tool.name}: {tool.description}")
    except Exception as e:
        print(f"❌ 列出工具失败: {e}")
    print()
    
    # 测试2: 列出资源
    print("📚 测试2: 列出可用资源")
    print("-" * 40)
    try:
        resources = await server.list_resources()
        print(f"发现 {len(resources)} 个资源:")
        for i, resource in enumerate(resources, 1):
            print(f"  {i}. {resource.name} ({resource.uri})")
    except Exception as e:
        print(f"❌ 列出资源失败: {e}")
    print()
    
    # 测试3: 列出支持的交易所
    print("🏢 测试3: 列出支持的交易所")
    print("-" * 40)
    try:
        result = await server.call_tool("list_exchanges", {})
        print(result[0].text)
    except Exception as e:
        print(f"❌ 列出交易所失败: {e}")
    print()
    
    # 测试4: 获取BTC价格 (使用Binance)
    print("💰 测试4: 获取BTC价格 (Binance)")
    print("-" * 40)
    try:
        result = await server.call_tool("get_btc_price", {
            "exchange": "binance",
            "symbol": "BTC/USDT"
        })
        print(result[0].text)
    except Exception as e:
        print(f"❌ 获取BTC价格失败: {e}")
    print()
    
    # 测试5: 获取ETH价格
    print("💎 测试5: 获取ETH价格")
    print("-" * 40)
    try:
        result = await server.call_tool("get_crypto_price", {
            "symbol": "ETH/USDT",
            "exchange": "binance"
        })
        print(result[0].text)
    except Exception as e:
        print(f"❌ 获取ETH价格失败: {e}")
    print()
    
    # 测试6: 获取市场概览
    print("📊 测试6: 获取市场概览")
    print("-" * 40)
    try:
        result = await server.call_tool("get_market_summary", {
            "exchange": "binance",
            "limit": 5
        })
        print(result[0].text)
    except Exception as e:
        print(f"❌ 获取市场概览失败: {e}")
    print()
    
    # 测试7: 读取交易所资源
    print("🔍 测试7: 读取Binance交易所信息")
    print("-" * 40)
    try:
        content = await server.read_resource("exchange://binance")
        print("Binance交易所信息:")
        print(content)
    except Exception as e:
        print(f"❌ 读取交易所信息失败: {e}")
    print()
    
    # 测试8: 获取K线数据
    print("📈 测试8: 获取BTC K线数据")
    print("-" * 40)
    try:
        result = await server.call_tool("get_ohlcv", {
            "symbol": "BTC/USDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 5
        })
        print(result[0].text)
    except Exception as e:
        print(f"❌ 获取K线数据失败: {e}")
    print()
    
    # 测试9: 查看缓存
    print("🗂️ 测试9: 查看价格缓存")
    print("-" * 40)
    try:
        content = await server.read_resource("cache://prices")
        print("价格缓存信息:")
        print(content)
    except Exception as e:
        print(f"❌ 查看缓存失败: {e}")
    print()
    
    # 测试10: 清除缓存
    print("🗑️ 测试10: 清除缓存")
    print("-" * 40)
    try:
        result = await server.call_tool("clear_cache", {})
        print(result[0].text)
    except Exception as e:
        print(f"❌ 清除缓存失败: {e}")
    print()
    
    print("🎉 所有测试完成!")
    print("=" * 60)

async def test_multiple_exchanges():
    """测试多个交易所"""
    print("\n🌐 测试多个交易所的BTC价格")
    print("=" * 60)
    
    exchanges = ["binance", "kraken", "coinbase"]
    
    for exchange in exchanges:
        print(f"\n📍 测试 {exchange.title()} 交易所:")
        print("-" * 30)
        try:
            result = await server.call_tool("get_btc_price", {
                "exchange": exchange,
                "symbol": "BTC/USDT" if exchange != "coinbase" else "BTC/USD"
            })
            print(result[0].text)
        except Exception as e:
            print(f"❌ {exchange} 交易所测试失败: {e}")

async def main():
    """主函数"""
    try:
        await test_btc_price_server()
        await test_multiple_exchanges()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试BTC价格MCP服务器...")
    print("注意: 需要网络连接来获取实时价格数据")
    print()
    asyncio.run(main())
