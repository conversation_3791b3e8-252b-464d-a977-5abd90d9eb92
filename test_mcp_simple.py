#!/usr/bin/env python3
"""
简单的MCP测试脚本
直接测试MCP服务器的功能
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们的服务器
from mcp_server_example import server

async def test_server_functions():
    """直接测试服务器功能"""
    
    print("🧪 直接测试MCP服务器功能")
    print("=" * 50)
    
    # 测试1: 列出工具
    print("📋 测试1: 列出可用工具")
    print("-" * 30)
    tools = await server.list_tools()
    print(f"发现 {len(tools)} 个工具:")
    for tool in tools:
        print(f"  • {tool.name}: {tool.description}")
    print()
    
    # 测试2: 列出资源
    print("📚 测试2: 列出可用资源")
    print("-" * 30)
    resources = await server.list_resources()
    print(f"发现 {len(resources)} 个资源:")
    for resource in resources:
        print(f"  • {resource.name} ({resource.uri})")
        print(f"    描述: {resource.description}")
    print()
    
    # 测试3: 读取资源
    print("📖 测试3: 读取资源")
    print("-" * 30)
    try:
        # 读取笔记资源
        note_content = await server.read_resource("note://note1")
        print(f"读取笔记1: {note_content}")
        
        # 读取待办事项资源
        todos_content = await server.read_resource("todos://all")
        print(f"待办事项列表:")
        print(todos_content)
    except Exception as e:
        print(f"读取资源时出错: {e}")
    print()
    
    # 测试4: 调用工具 - 计算器
    print("🧮 测试4: 使用计算器工具")
    print("-" * 30)
    try:
        calc_result = await server.call_tool("calculate", {"expression": "10 + 5 * 2"})
        print(f"计算结果: {calc_result[0].text}")
    except Exception as e:
        print(f"计算时出错: {e}")
    print()
    
    # 测试5: 调用工具 - 添加笔记
    print("📝 测试5: 添加新笔记")
    print("-" * 30)
    try:
        add_note_result = await server.call_tool("add_note", {
            "note_id": "test_note",
            "content": "这是通过直接调用添加的测试笔记"
        })
        print(f"添加笔记结果: {add_note_result[0].text}")
    except Exception as e:
        print(f"添加笔记时出错: {e}")
    print()
    
    # 测试6: 调用工具 - 获取刚添加的笔记
    print("🔍 测试6: 获取刚添加的笔记")
    print("-" * 30)
    try:
        get_note_result = await server.call_tool("get_note", {"note_id": "test_note"})
        print(f"获取笔记结果: {get_note_result[0].text}")
    except Exception as e:
        print(f"获取笔记时出错: {e}")
    print()
    
    # 测试7: 调用工具 - 添加待办事项
    print("✅ 测试7: 添加待办事项")
    print("-" * 30)
    try:
        add_todo_result = await server.call_tool("add_todo", {
            "task": "通过直接调用测试完成"
        })
        print(f"添加待办事项结果: {add_todo_result[0].text}")
    except Exception as e:
        print(f"添加待办事项时出错: {e}")
    print()
    
    # 测试8: 调用工具 - 完成待办事项
    print("🎯 测试8: 完成待办事项")
    print("-" * 30)
    try:
        complete_todo_result = await server.call_tool("complete_todo", {"todo_id": 1})
        print(f"完成待办事项结果: {complete_todo_result[0].text}")
    except Exception as e:
        print(f"完成待办事项时出错: {e}")
    print()
    
    # 测试9: 再次读取资源查看变化
    print("🔄 测试9: 查看更新后的资源")
    print("-" * 30)
    try:
        # 重新读取待办事项资源
        updated_todos_content = await server.read_resource("todos://all")
        print("更新后的待办事项列表:")
        print(updated_todos_content)
    except Exception as e:
        print(f"读取更新后的资源时出错: {e}")
    print()
    
    # 测试10: 测试错误处理
    print("⚠️ 测试10: 错误处理")
    print("-" * 30)
    try:
        # 尝试获取不存在的笔记
        error_result = await server.call_tool("get_note", {"note_id": "nonexistent"})
        print(f"获取不存在笔记的结果: {error_result[0].text}")
    except Exception as e:
        print(f"预期的错误: {e}")
    
    try:
        # 尝试无效的计算
        error_calc = await server.call_tool("calculate", {"expression": "invalid_expression"})
        print(f"无效计算的结果: {error_calc[0].text}")
    except Exception as e:
        print(f"计算错误: {e}")
    print()
    
    print("🎉 所有测试完成!")
    print("=" * 50)

async def main():
    """主函数"""
    try:
        await test_server_functions()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
