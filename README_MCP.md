# MCP (Model Context Protocol) 示例

这个项目包含了MCP的完整示例，展示了如何创建和使用MCP服务器和客户端。

## 📁 文件说明

- `mcp_server_example.py` - MCP服务器示例，提供工具和资源
- `mcp_client_example.py` - MCP客户端示例，连接并测试服务器
- `demo_mcp.py` - MCP概念演示脚本
- `test_mcp_simple.py` - 简单的功能测试脚本

## 🚀 快速开始

### 1. 安装依赖

确保你已经安装了Python 3.11和MCP包：

```bash
# 使用Python 3.11
py -3.11 -m pip install mcp
```

### 2. 运行演示

首先运行概念演示，了解MCP的基本概念：

```bash
py -3.11 demo_mcp.py
```

### 3. 启动MCP服务器

在一个终端中启动服务器：

```bash
py -3.11 mcp_server_example.py
```

服务器启动后会显示：
```
启动MCP服务器...
服务器提供以下功能:
- 笔记管理 (添加、获取笔记)
- 待办事项管理 (添加、完成待办事项)
- 简单计算器
- 资源访问 (笔记和待办事项)

等待客户端连接...
```

### 4. 运行客户端测试

在另一个终端中运行客户端：

```bash
py -3.11 mcp_client_example.py
```

## 🔧 服务器功能

### 工具 (Tools)

1. **add_note** - 添加新笔记
   - 参数: `note_id` (字符串), `content` (字符串)
   - 示例: `{"note_id": "my_note", "content": "这是我的笔记"}`

2. **get_note** - 获取笔记
   - 参数: `note_id` (字符串)
   - 示例: `{"note_id": "my_note"}`

3. **add_todo** - 添加待办事项
   - 参数: `task` (字符串)
   - 示例: `{"task": "完成项目文档"}`

4. **complete_todo** - 完成待办事项
   - 参数: `todo_id` (整数)
   - 示例: `{"todo_id": 1}`

5. **calculate** - 数学计算
   - 参数: `expression` (字符串)
   - 示例: `{"expression": "10 + 5 * 2"}`

### 资源 (Resources)

1. **笔记资源** - `note://{note_id}`
   - 访问特定笔记的内容
   - 示例: `note://note1`

2. **待办事项资源** - `todos://all`
   - 获取所有待办事项的JSON列表

## 🧪 测试示例

### 基本工具调用

```python
# 计算器
result = await session.call_tool("calculate", {"expression": "2 + 3 * 4"})
# 结果: "2 + 3 * 4 = 14"

# 添加笔记
result = await session.call_tool("add_note", {
    "note_id": "meeting_notes", 
    "content": "今天的会议要点..."
})

# 获取笔记
result = await session.call_tool("get_note", {"note_id": "meeting_notes"})
```

### 资源访问

```python
# 读取笔记
content = await session.read_resource("note://meeting_notes")

# 读取待办事项
todos = await session.read_resource("todos://all")
```

## 🏗️ MCP架构

```
┌─────────────────┐    MCP协议    ┌─────────────────┐
│   MCP客户端     │ ◄──────────► │   MCP服务器     │
│  (AI应用)       │   JSON-RPC   │  (工具提供者)   │
└─────────────────┘              └─────────────────┘
        │                                │
        ├─ 列出工具                      ├─ 工具实现
        ├─ 调用工具                      ├─ 资源管理
        ├─ 列出资源                      ├─ 数据处理
        └─ 读取资源                      └─ 业务逻辑
```

## 💡 扩展建议

1. **添加更多工具**
   - 文件操作工具
   - 网络请求工具
   - 数据库操作工具

2. **增强资源管理**
   - 动态资源发现
   - 资源缓存机制
   - 权限控制

3. **改进错误处理**
   - 详细的错误信息
   - 重试机制
   - 日志记录

4. **性能优化**
   - 异步处理
   - 连接池
   - 批量操作

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 确保服务器已启动
   - 检查端口是否被占用
   - 验证Python路径

2. **工具调用失败**
   - 检查参数格式
   - 验证必需参数
   - 查看错误日志

3. **资源访问失败**
   - 确认资源URI格式
   - 检查资源是否存在
   - 验证权限设置

## 📚 更多资源

- [MCP官方文档](https://modelcontextprotocol.io/)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [MCP示例项目](https://github.com/modelcontextprotocol)

## 🤝 贡献

欢迎提交问题和改进建议！

---

**注意**: 这是一个学习示例，生产环境使用时请添加适当的安全措施和错误处理。
