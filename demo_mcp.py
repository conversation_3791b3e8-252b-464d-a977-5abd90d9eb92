#!/usr/bin/env python3
"""
MCP演示脚本
展示MCP的基本概念和用法
"""

import asyncio
import json
from mcp.types import Tool, Resource, TextContent

# 模拟数据
notes_db = {
    "note1": "这是第一个笔记",
    "note2": "这是第二个笔记",
    "note3": "这是关于MCP的笔记"
}

todos_db = [
    {"id": 1, "task": "学习MCP协议", "completed": False},
    {"id": 2, "task": "创建示例项目", "completed": True},
    {"id": 3, "task": "测试MCP功能", "completed": False}
]

def demo_mcp_concepts():
    """演示MCP的基本概念"""
    
    print("🎯 MCP (Model Context Protocol) 演示")
    print("=" * 60)
    print()
    
    print("📖 什么是MCP?")
    print("-" * 30)
    print("MCP是一个开放协议，用于连接AI助手和外部数据源/工具。")
    print("它允许AI模型安全地访问和操作外部系统。")
    print()
    
    print("🏗️ MCP的主要组件:")
    print("-" * 30)
    print("1. 服务器 (Server) - 提供工具和资源")
    print("2. 客户端 (Client) - 使用工具和资源的AI应用")
    print("3. 工具 (Tools) - 可执行的功能")
    print("4. 资源 (Resources) - 可读取的数据")
    print()
    
    # 演示工具定义
    print("🔧 工具 (Tools) 示例:")
    print("-" * 30)
    
    tools = [
        Tool(
            name="add_note",
            description="添加一个新笔记",
            inputSchema={
                "type": "object",
                "properties": {
                    "note_id": {"type": "string", "description": "笔记ID"},
                    "content": {"type": "string", "description": "笔记内容"}
                },
                "required": ["note_id", "content"]
            }
        ),
        Tool(
            name="calculate",
            description="执行数学计算",
            inputSchema={
                "type": "object",
                "properties": {
                    "expression": {"type": "string", "description": "数学表达式"}
                },
                "required": ["expression"]
            }
        )
    ]
    
    for i, tool in enumerate(tools, 1):
        print(f"{i}. {tool.name}: {tool.description}")
    print()
    
    # 演示资源定义
    print("📚 资源 (Resources) 示例:")
    print("-" * 30)
    
    resources = []
    for note_id, content in notes_db.items():
        resources.append(
            Resource(
                uri=f"note://{note_id}",
                name=f"笔记: {note_id}",
                description=f"内容: {content[:20]}...",
                mimeType="text/plain"
            )
        )
    
    resources.append(
        Resource(
            uri="todos://all",
            name="待办事项列表",
            description="所有待办事项",
            mimeType="application/json"
        )
    )
    
    for i, resource in enumerate(resources, 1):
        print(f"{i}. {resource.name} ({resource.uri})")
        print(f"   描述: {resource.description}")
    print()

def demo_tool_execution():
    """演示工具执行"""
    
    print("⚙️ 工具执行演示:")
    print("-" * 30)
    
    # 演示计算器工具
    def calculate_tool(expression: str) -> str:
        try:
            # 简单的安全计算
            allowed_chars = set("0123456789+-*/().")
            if not all(c in allowed_chars or c.isspace() for c in expression):
                return f"错误: 表达式包含不允许的字符"
            
            result = eval(expression)
            return f"{expression} = {result}"
        except Exception as e:
            return f"计算错误: {str(e)}"
    
    # 演示添加笔记工具
    def add_note_tool(note_id: str, content: str) -> str:
        notes_db[note_id] = content
        return f"成功添加笔记 '{note_id}': {content}"
    
    # 演示获取笔记工具
    def get_note_tool(note_id: str) -> str:
        if note_id in notes_db:
            return f"笔记 '{note_id}': {notes_db[note_id]}"
        else:
            return f"笔记 '{note_id}' 不存在"
    
    # 执行一些示例
    print("1. 计算器工具:")
    print(f"   输入: '10 + 5 * 2'")
    print(f"   输出: {calculate_tool('10 + 5 * 2')}")
    print()
    
    print("2. 添加笔记工具:")
    print(f"   输入: note_id='demo_note', content='这是演示笔记'")
    print(f"   输出: {add_note_tool('demo_note', '这是演示笔记')}")
    print()
    
    print("3. 获取笔记工具:")
    print(f"   输入: note_id='demo_note'")
    print(f"   输出: {get_note_tool('demo_note')}")
    print()

def demo_resource_access():
    """演示资源访问"""
    
    print("📖 资源访问演示:")
    print("-" * 30)
    
    def read_resource(uri: str) -> str:
        if uri.startswith("note://"):
            note_id = uri.replace("note://", "")
            if note_id in notes_db:
                return notes_db[note_id]
            else:
                return f"笔记 {note_id} 不存在"
        
        elif uri == "todos://all":
            return json.dumps(todos_db, ensure_ascii=False, indent=2)
        
        else:
            return f"未知的资源URI: {uri}"
    
    # 演示资源读取
    print("1. 读取笔记资源:")
    print(f"   URI: 'note://note1'")
    print(f"   内容: {read_resource('note://note1')}")
    print()
    
    print("2. 读取待办事项资源:")
    print(f"   URI: 'todos://all'")
    print(f"   内容:")
    print(f"   {read_resource('todos://all')}")
    print()

def demo_mcp_workflow():
    """演示MCP工作流程"""
    
    print("🔄 MCP工作流程演示:")
    print("-" * 30)
    print("1. 客户端连接到服务器")
    print("2. 客户端请求可用工具列表")
    print("3. 客户端请求可用资源列表")
    print("4. 客户端调用工具执行任务")
    print("5. 客户端读取资源获取数据")
    print("6. 服务器返回结果")
    print()
    
    print("💡 MCP的优势:")
    print("-" * 30)
    print("• 标准化的协议，易于集成")
    print("• 安全的工具和资源访问")
    print("• 支持异步操作")
    print("• 可扩展的架构")
    print("• 跨语言支持")
    print()

def main():
    """主函数"""
    
    print("🚀 欢迎使用MCP演示!")
    print()
    
    demo_mcp_concepts()
    demo_tool_execution()
    demo_resource_access()
    demo_mcp_workflow()
    
    print("🎉 演示完成!")
    print("=" * 60)
    print()
    print("📝 接下来你可以:")
    print("• 运行 'py -3.11 mcp_server_example.py' 启动MCP服务器")
    print("• 在另一个终端运行客户端连接到服务器")
    print("• 探索更多MCP功能和用例")
    print()

if __name__ == "__main__":
    main()
