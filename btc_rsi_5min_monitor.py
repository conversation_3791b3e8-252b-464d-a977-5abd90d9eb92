#!/usr/bin/env python3
"""
BTC 5分钟RSI监控器
每5分钟计算RSI并用彩色表格显示
"""

import asyncio
import ccxt.async_support as ccxt
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict
import time
import os

# ANSI颜色代码
class Colors:
    RED = '\033[91m'      # 红色 (RSI < 25)
    GREEN = '\033[92m'    # 绿色 (RSI > 75)
    YELLOW = '\033[93m'   # 黄色 (25 <= RSI <= 75)
    BLUE = '\033[94m'     # 蓝色
    PURPLE = '\033[95m'   # 紫色
    CYAN = '\033[96m'     # 青色
    WHITE = '\033[97m'    # 白色
    BOLD = '\033[1m'      # 粗体
    UNDERLINE = '\033[4m' # 下划线
    END = '\033[0m'       # 结束颜色

def calculate_rsi(prices: List[float], period: int = 14) -> float:
    """
    计算RSI (Relative Strength Index)
    """
    if len(prices) < period + 1:
        return None
    
    # 计算价格变化
    deltas = np.diff(prices)
    
    # 分离上涨和下跌
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    # 计算平均收益和平均损失
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])
    
    # 计算后续的平均值（使用指数移动平均）
    for i in range(period, len(gains)):
        avg_gain = (avg_gain * (period - 1) + gains[i]) / period
        avg_loss = (avg_loss * (period - 1) + losses[i]) / period
    
    # 计算RSI
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return round(rsi, 2)

def format_rsi_value(rsi: float) -> str:
    """
    根据RSI值返回带颜色的格式化字符串
    """
    if rsi is None:
        return f"{Colors.WHITE}--{Colors.END}"
    
    rsi_str = f"{rsi:6.2f}"
    
    if rsi < 25:
        return f"{Colors.RED}{Colors.BOLD}{rsi_str}{Colors.END}"  # 红色粗体
    elif rsi > 75:
        return f"{Colors.GREEN}{Colors.BOLD}{rsi_str}{Colors.END}"  # 绿色粗体
    else:
        return f"{Colors.YELLOW}{rsi_str}{Colors.END}"  # 黄色

def get_rsi_signal(rsi: float) -> str:
    """
    获取RSI信号描述
    """
    if rsi is None:
        return "无数据"
    
    if rsi < 25:
        return f"{Colors.RED}强烈超卖{Colors.END}"
    elif rsi < 30:
        return f"{Colors.RED}超卖{Colors.END}"
    elif rsi > 75:
        return f"{Colors.GREEN}强烈超买{Colors.END}"
    elif rsi > 70:
        return f"{Colors.GREEN}超买{Colors.END}"
    elif 45 <= rsi <= 55:
        return f"{Colors.CYAN}中性{Colors.END}"
    else:
        return f"{Colors.YELLOW}正常{Colors.END}"

async def get_btc_5min_data(exchange_name: str = 'binance', limit: int = 100) -> List[Dict]:
    """获取BTC 5分钟K线数据"""
    
    if exchange_name.lower() == 'binance':
        exchange = ccxt.binance()
    elif exchange_name.lower() == 'kraken':
        exchange = ccxt.kraken()
    elif exchange_name.lower() == 'coinbase':
        exchange = ccxt.coinbase()
    else:
        exchange = ccxt.binance()
    
    exchange.timeout = 30000
    exchange.enableRateLimit = True
    
    try:
        symbol = 'BTC/USDT' if exchange_name != 'kraken' else 'BTC/USD'
        ohlcv = await exchange.fetch_ohlcv(symbol, '5m', limit=limit)
        
        data = []
        for candle in ohlcv:
            data.append({
                'timestamp': candle[0],
                'datetime': datetime.fromtimestamp(candle[0] / 1000),
                'open': candle[1],
                'high': candle[2],
                'low': candle[3],
                'close': candle[4],
                'volume': candle[5]
            })
        
        return data
    
    except Exception as e:
        print(f"{Colors.RED}获取数据失败: {e}{Colors.END}")
        return []
    
    finally:
        await exchange.close()

def print_header():
    """打印表格头部"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}{'='*80}{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}🚀 BTC 5分钟RSI监控器{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}{'='*80}{Colors.END}")
    print(f"{Colors.WHITE}更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Colors.END}")
    print(f"{Colors.WHITE}数据来源: Binance BTC/USDT{Colors.END}")
    print(f"{Colors.RED}🔴 RSI < 25: 强烈超卖{Colors.END} | {Colors.GREEN}🟢 RSI > 75: 强烈超买{Colors.END} | {Colors.YELLOW}🟡 其他: 正常区间{Colors.END}")
    print()

def print_rsi_table(data: List[Dict], rsi_values: List[float]):
    """打印RSI表格"""
    
    # 表格头部
    print(f"{Colors.BLUE}{Colors.BOLD}{'时间':<12} {'价格':<10} {'RSI(14)':<8} {'RSI(7)':<8} {'RSI(21)':<8} {'信号':<12} {'成交量':<10}{Colors.END}")
    print(f"{Colors.BLUE}{'-'*75}{Colors.END}")
    
    # 显示最近20条数据
    recent_data = data[-20:]
    recent_rsi = rsi_values[-20:]
    
    for i, (candle, rsi_14) in enumerate(zip(recent_data, recent_rsi)):
        # 计算不同周期的RSI
        if i >= 6:  # 确保有足够数据计算RSI(7)
            close_prices_7 = [d['close'] for d in recent_data[max(0, i-6):i+1]]
            rsi_7 = calculate_rsi(close_prices_7, 7)
        else:
            rsi_7 = None
            
        if i >= 20:  # 确保有足够数据计算RSI(21)
            close_prices_21 = [d['close'] for d in recent_data[max(0, i-20):i+1]]
            rsi_21 = calculate_rsi(close_prices_21, 21)
        else:
            rsi_21 = None
        
        time_str = candle['datetime'].strftime('%H:%M')
        price_str = f"${candle['close']:,.0f}"
        volume_str = f"{candle['volume']:.0f}"
        
        rsi_14_colored = format_rsi_value(rsi_14)
        rsi_7_colored = format_rsi_value(rsi_7)
        rsi_21_colored = format_rsi_value(rsi_21)
        signal = get_rsi_signal(rsi_14)
        
        print(f"{time_str:<12} {price_str:<10} {rsi_14_colored:<15} {rsi_7_colored:<15} {rsi_21_colored:<15} {signal:<20} {volume_str:<10}")

def print_summary(current_price: float, rsi_current: float, rsi_values: List[float]):
    """打印摘要信息"""
    
    print(f"\n{Colors.CYAN}{Colors.BOLD}📊 当前状态摘要{Colors.END}")
    print(f"{Colors.BLUE}{'-'*40}{Colors.END}")
    
    # 当前价格和RSI
    print(f"💰 当前价格: {Colors.WHITE}{Colors.BOLD}${current_price:,.2f}{Colors.END}")
    print(f"📈 当前RSI(14): {format_rsi_value(rsi_current)}")
    print(f"🎯 信号: {get_rsi_signal(rsi_current)}")
    
    # RSI统计
    valid_rsi = [rsi for rsi in rsi_values if rsi is not None]
    if valid_rsi:
        rsi_min = min(valid_rsi)
        rsi_max = max(valid_rsi)
        rsi_avg = np.mean(valid_rsi)
        
        print(f"\n📊 RSI统计 (最近{len(valid_rsi)}个周期):")
        print(f"   最低: {format_rsi_value(rsi_min)}")
        print(f"   最高: {format_rsi_value(rsi_max)}")
        print(f"   平均: {format_rsi_value(rsi_avg)}")
        
        # 计算超买超卖次数
        oversold_count = sum(1 for rsi in valid_rsi if rsi < 30)
        overbought_count = sum(1 for rsi in valid_rsi if rsi > 70)
        
        print(f"\n🔍 信号统计:")
        print(f"   超卖次数 (RSI<30): {Colors.RED}{oversold_count}{Colors.END}")
        print(f"   超买次数 (RSI>70): {Colors.GREEN}{overbought_count}{Colors.END}")

async def monitor_btc_rsi():
    """监控BTC RSI"""
    
    print(f"{Colors.CYAN}🚀 启动BTC 5分钟RSI监控器...{Colors.END}")
    print(f"{Colors.YELLOW}正在获取历史数据...{Colors.END}")
    
    while True:
        try:
            # 清屏
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # 获取数据
            data = await get_btc_5min_data('binance', 100)
            
            if not data:
                print(f"{Colors.RED}❌ 无法获取数据，5分钟后重试...{Colors.END}")
                await asyncio.sleep(300)  # 5分钟
                continue
            
            # 计算RSI
            close_prices = [candle['close'] for candle in data]
            rsi_values = []
            
            for i in range(len(close_prices)):
                if i >= 14:  # 需要至少15个数据点来计算RSI(14)
                    prices_subset = close_prices[i-14:i+1]
                    rsi = calculate_rsi(prices_subset, 14)
                    rsi_values.append(rsi)
                else:
                    rsi_values.append(None)
            
            # 打印表格
            print_header()
            print_rsi_table(data, rsi_values)
            
            # 打印摘要
            current_price = data[-1]['close']
            current_rsi = rsi_values[-1] if rsi_values else None
            print_summary(current_price, current_rsi, rsi_values)
            
            # 下次更新提示
            next_update = datetime.now() + timedelta(minutes=5)
            print(f"\n{Colors.PURPLE}⏰ 下次更新: {next_update.strftime('%H:%M:%S')}{Colors.END}")
            print(f"{Colors.PURPLE}按 Ctrl+C 停止监控{Colors.END}")
            
            # 等待5分钟
            await asyncio.sleep(300)
            
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 监控已停止{Colors.END}")
            break
        except Exception as e:
            print(f"{Colors.RED}❌ 发生错误: {e}{Colors.END}")
            print(f"{Colors.YELLOW}5分钟后重试...{Colors.END}")
            await asyncio.sleep(300)

async def single_check():
    """单次检查RSI"""
    
    print(f"{Colors.CYAN}🔍 获取BTC当前RSI数据...{Colors.END}")
    
    try:
        # 获取数据
        data = await get_btc_5min_data('binance', 50)
        
        if not data:
            print(f"{Colors.RED}❌ 无法获取数据{Colors.END}")
            return
        
        # 计算RSI
        close_prices = [candle['close'] for candle in data]
        rsi_values = []
        
        for i in range(len(close_prices)):
            if i >= 14:
                prices_subset = close_prices[i-14:i+1]
                rsi = calculate_rsi(prices_subset, 14)
                rsi_values.append(rsi)
            else:
                rsi_values.append(None)
        
        # 打印结果
        print_header()
        print_rsi_table(data, rsi_values)
        
        current_price = data[-1]['close']
        current_rsi = rsi_values[-1] if rsi_values else None
        print_summary(current_price, current_rsi, rsi_values)
        
    except Exception as e:
        print(f"{Colors.RED}❌ 发生错误: {e}{Colors.END}")

async def main():
    """主函数"""
    
    print(f"{Colors.BOLD}{Colors.CYAN}🚀 BTC RSI 监控器{Colors.END}")
    print(f"{Colors.WHITE}选择模式:{Colors.END}")
    print(f"{Colors.GREEN}1. 单次检查{Colors.END}")
    print(f"{Colors.YELLOW}2. 持续监控 (每5分钟更新){Colors.END}")
    
    try:
        choice = input(f"\n{Colors.CYAN}请选择 (1 或 2): {Colors.END}").strip()
        
        if choice == '1':
            await single_check()
        elif choice == '2':
            await monitor_btc_rsi()
        else:
            print(f"{Colors.RED}无效选择，默认执行单次检查{Colors.END}")
            await single_check()
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 程序已退出{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ 程序错误: {e}{Colors.END}")

if __name__ == "__main__":
    asyncio.run(main())
