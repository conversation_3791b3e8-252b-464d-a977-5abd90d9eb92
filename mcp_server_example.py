#!/usr/bin/env python3
"""
简单的MCP服务器示例
提供基本的工具和资源功能
"""

import asyncio
import json
from typing import Any, Sequence
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    CallToolRequest,
    CallToolResult,
    ListResourcesRequest,
    ListResourcesResult,
    ListToolsRequest,
    ListToolsResult,
    ReadResourceRequest,
    ReadResourceResult,
)

# 创建服务器实例
server = Server("example-server")

# 模拟一些数据
NOTES = {
    "note1": "这是第一个笔记",
    "note2": "这是第二个笔记",
    "note3": "这是关于MCP的笔记"
}

TODOS = [
    {"id": 1, "task": "学习MCP协议", "completed": False},
    {"id": 2, "task": "创建示例项目", "completed": True},
    {"id": 3, "task": "测试MCP功能", "completed": False}
]

@server.list_resources()
async def list_resources() -> list[Resource]:
    """列出可用的资源"""
    resources = []
    
    # 添加笔记资源
    for note_id in NOTES:
        resources.append(
            Resource(
                uri=f"note://{note_id}",
                name=f"笔记: {note_id}",
                description=f"笔记内容: {NOTES[note_id][:20]}...",
                mimeType="text/plain"
            )
        )
    
    # 添加待办事项资源
    resources.append(
        Resource(
            uri="todos://all",
            name="待办事项列表",
            description="所有待办事项的列表",
            mimeType="application/json"
        )
    )
    
    return resources

@server.read_resource()
async def read_resource(uri: str) -> str:
    """读取指定的资源"""
    if uri.startswith("note://"):
        note_id = uri.replace("note://", "")
        if note_id in NOTES:
            return NOTES[note_id]
        else:
            raise ValueError(f"笔记 {note_id} 不存在")
    
    elif uri == "todos://all":
        return json.dumps(TODOS, ensure_ascii=False, indent=2)
    
    else:
        raise ValueError(f"未知的资源URI: {uri}")

@server.list_tools()
async def list_tools() -> list[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="add_note",
            description="添加一个新笔记",
            inputSchema={
                "type": "object",
                "properties": {
                    "note_id": {
                        "type": "string",
                        "description": "笔记的唯一标识符"
                    },
                    "content": {
                        "type": "string",
                        "description": "笔记的内容"
                    }
                },
                "required": ["note_id", "content"]
            }
        ),
        Tool(
            name="get_note",
            description="获取指定的笔记",
            inputSchema={
                "type": "object",
                "properties": {
                    "note_id": {
                        "type": "string",
                        "description": "要获取的笔记ID"
                    }
                },
                "required": ["note_id"]
            }
        ),
        Tool(
            name="add_todo",
            description="添加一个新的待办事项",
            inputSchema={
                "type": "object",
                "properties": {
                    "task": {
                        "type": "string",
                        "description": "待办事项的描述"
                    }
                },
                "required": ["task"]
            }
        ),
        Tool(
            name="complete_todo",
            description="标记待办事项为完成",
            inputSchema={
                "type": "object",
                "properties": {
                    "todo_id": {
                        "type": "integer",
                        "description": "要完成的待办事项ID"
                    }
                },
                "required": ["todo_id"]
            }
        ),
        Tool(
            name="calculate",
            description="执行简单的数学计算",
            inputSchema={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的数学表达式，例如: '2 + 3 * 4'"
                    }
                },
                "required": ["expression"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: dict[str, Any]) -> list[TextContent]:
    """执行工具调用"""
    
    if name == "add_note":
        note_id = arguments["note_id"]
        content = arguments["content"]
        NOTES[note_id] = content
        return [TextContent(type="text", text=f"成功添加笔记 '{note_id}': {content}")]
    
    elif name == "get_note":
        note_id = arguments["note_id"]
        if note_id in NOTES:
            return [TextContent(type="text", text=f"笔记 '{note_id}': {NOTES[note_id]}")]
        else:
            return [TextContent(type="text", text=f"笔记 '{note_id}' 不存在")]
    
    elif name == "add_todo":
        task = arguments["task"]
        new_id = max([todo["id"] for todo in TODOS], default=0) + 1
        new_todo = {"id": new_id, "task": task, "completed": False}
        TODOS.append(new_todo)
        return [TextContent(type="text", text=f"成功添加待办事项 {new_id}: {task}")]
    
    elif name == "complete_todo":
        todo_id = arguments["todo_id"]
        for todo in TODOS:
            if todo["id"] == todo_id:
                todo["completed"] = True
                return [TextContent(type="text", text=f"待办事项 {todo_id} 已标记为完成")]
        return [TextContent(type="text", text=f"待办事项 {todo_id} 不存在")]
    
    elif name == "calculate":
        expression = arguments["expression"]
        try:
            # 简单的安全计算（仅支持基本数学运算）
            allowed_chars = set("0123456789+-*/().")
            if not all(c in allowed_chars or c.isspace() for c in expression):
                raise ValueError("表达式包含不允许的字符")
            
            result = eval(expression)
            return [TextContent(type="text", text=f"{expression} = {result}")]
        except Exception as e:
            return [TextContent(type="text", text=f"计算错误: {str(e)}")]
    
    else:
        raise ValueError(f"未知的工具: {name}")

async def main():
    """启动MCP服务器"""
    print("启动MCP服务器...")
    print("服务器提供以下功能:")
    print("- 笔记管理 (添加、获取笔记)")
    print("- 待办事项管理 (添加、完成待办事项)")
    print("- 简单计算器")
    print("- 资源访问 (笔记和待办事项)")
    print("\n等待客户端连接...")
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
