# BTC价格MCP服务器

这是一个基于MCP (Model Context Protocol) 的比特币和加密货币价格服务器，提供实时市场数据和技术分析功能。

## 🚀 功能特点

### 🔧 工具 (Tools)

1. **get_btc_price** - 获取BTC当前价格
   - 支持多个交易所
   - 包含24小时价格变化
   - 显示最高价、最低价、成交量

2. **get_crypto_price** - 获取任意加密货币价格
   - 支持所有主流加密货币
   - 实时买卖价差
   - 24小时统计数据

3. **get_market_summary** - 获取市场概览
   - 按交易量排序的热门币种
   - 市场趋势分析
   - 可自定义返回数量

4. **get_ohlcv** - 获取K线数据
   - 支持多种时间周期 (1m, 5m, 15m, 1h, 4h, 1d)
   - OHLCV完整数据
   - 历史价格分析

5. **list_exchanges** - 列出支持的交易所
   - 显示所有可用交易所
   - 交易所状态信息

6. **clear_cache** - 清除价格缓存
   - 管理缓存数据
   - 提高数据新鲜度

### 📚 资源 (Resources)

1. **exchange://{name}** - 交易所信息
   - 交易所基本信息
   - 支持的功能列表
   - 可用交易对统计

2. **cache://prices** - 价格缓存状态
   - 缓存项目统计
   - 数据年龄信息
   - 缓存管理

### 🏢 支持的交易所

- **Binance** - 全球最大的加密货币交易所
- **Coinbase** - 美国主要交易所
- **Kraken** - 欧洲知名交易所
- **Huobi** - 亚洲主要交易所
- **OKX** - 全球化交易平台
- **Bybit** - 衍生品交易所
- **KuCoin** - 全球化数字资产交易所

## 📦 安装和设置

### 1. 安装依赖

```bash
# 安装Python 3.11
winget install Python.Python.3.11

# 安装MCP和CCXT
py -3.11 -m pip install mcp ccxt
```

### 2. 文件说明

- `btc_price_mcp_server.py` - 主要的MCP服务器
- `btc_price_demo_offline.py` - 离线演示脚本
- `demo_btc_price.py` - 在线演示脚本 (需要网络)
- `test_btc_price.py` - 测试脚本

### 3. 运行演示

```bash
# 运行离线演示 (推荐先运行)
py -3.11 btc_price_demo_offline.py

# 运行在线演示 (需要网络连接)
py -3.11 demo_btc_price.py

# 启动MCP服务器
py -3.11 btc_price_mcp_server.py
```

## 🔧 Claude Desktop配置

### 配置文件位置

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

### 配置示例

```json
{
  "mcpServers": {
    "btc-price": {
      "command": "python",
      "args": ["C:/Users/<USER>/Documents/myprojects/btc_price_mcp_server.py"]
    }
  }
}
```

## 💡 使用示例

配置完成后，你可以在Claude Desktop中直接询问：

### 基本价格查询
- "BTC现在多少钱？"
- "以太坊的当前价格是多少？"
- "显示SOL/USDT的价格"

### 市场分析
- "显示前10名加密货币的价格"
- "Binance上的热门交易对有哪些？"
- "比较不同交易所的BTC价格"

### 技术分析
- "显示BTC的1小时K线数据"
- "分析ETH的24小时价格走势"
- "获取BTC的历史价格数据"

### 交易所信息
- "列出所有支持的交易所"
- "Binance交易所有哪些功能？"
- "显示缓存的价格数据"

## 🛠️ 技术架构

### 核心技术栈

- **MCP (Model Context Protocol)** - AI助手与外部数据源的连接协议
- **CCXT** - 统一的加密货币交易所API库
- **Python 3.11** - 主要编程语言
- **asyncio** - 异步编程支持

### 数据流程

```
Claude Desktop → MCP协议 → BTC价格服务器 → CCXT → 交易所API → 实时数据
```

### 缓存机制

- 价格数据缓存60秒
- 减少API调用频率
- 提高响应速度
- 支持手动清除缓存

## 🔍 故障排除

### 常见问题

1. **网络连接错误**
   - 检查网络连接
   - 确认交易所API可访问
   - 尝试不同的交易所

2. **交易对不存在**
   - 验证交易对格式 (如: BTC/USDT)
   - 检查交易所是否支持该交易对
   - 使用list_exchanges查看支持的交易所

3. **API限制**
   - 等待一段时间后重试
   - 使用不同的交易所
   - 检查是否触发了速率限制

### 调试技巧

```bash
# 运行离线演示测试基本功能
py -3.11 btc_price_demo_offline.py

# 检查网络连接
py -3.11 demo_btc_price.py

# 验证MCP服务器
py -3.11 btc_price_mcp_server.py
```

## 🚀 扩展功能

### 可以添加的功能

1. **更多技术指标**
   - RSI、MACD、布林带
   - 移动平均线
   - 成交量分析

2. **价格预警**
   - 价格阈值监控
   - 邮件/短信通知
   - 自定义预警条件

3. **投资组合管理**
   - 持仓跟踪
   - 盈亏计算
   - 风险评估

4. **更多数据源**
   - 新闻数据
   - 社交媒体情绪
   - 链上数据分析

## 📊 性能优化

### 建议的优化

1. **缓存策略**
   - 根据数据类型设置不同缓存时间
   - 实现智能缓存更新
   - 添加缓存预热机制

2. **并发处理**
   - 并行请求多个交易所
   - 异步数据处理
   - 连接池管理

3. **错误处理**
   - 重试机制
   - 降级策略
   - 详细错误日志

## 📚 相关资源

### 官方文档

- [MCP官方文档](https://modelcontextprotocol.io/)
- [CCXT文档](https://docs.ccxt.com/)
- [Python asyncio文档](https://docs.python.org/3/library/asyncio.html)

### 社区资源

- [MCP GitHub](https://github.com/modelcontextprotocol)
- [CCXT GitHub](https://github.com/ccxt/ccxt)
- [加密货币API文档](https://github.com/public-apis/public-apis#cryptocurrency)

## 🤝 贡献

欢迎提交问题和改进建议！

### 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件

---

**注意**: 这是一个学习和演示项目。在生产环境中使用时，请添加适当的错误处理、安全措施和监控功能。加密货币投资有风险，请谨慎决策。
