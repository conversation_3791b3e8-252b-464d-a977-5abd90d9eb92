#!/usr/bin/env python3
"""
BTC价格MCP服务器
基于CCXT库，提供实时加密货币价格和市场数据
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
import ccxt.async_support as ccxt
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    CallToolRequest,
    CallToolResult,
    ListResourcesRequest,
    ListResourcesResult,
    ListToolsRequest,
    ListToolsResult,
    ReadResourceRequest,
    ReadResourceResult,
)

# 创建服务器实例
server = Server("btc-price-server")

# 支持的交易所
SUPPORTED_EXCHANGES = {
    'binance': ccxt.binance,
    'coinbase': ccxt.coinbase,
    'kraken': ccxt.kraken,
    'huobi': ccxt.huobi,
    'okx': ccxt.okx,
    'bybit': ccxt.bybit,
    'kucoin': ccxt.kucoin,
}

# 缓存数据
price_cache = {}
cache_timestamp = {}

async def get_exchange(exchange_name: str):
    """获取交易所实例"""
    if exchange_name.lower() not in SUPPORTED_EXCHANGES:
        raise ValueError(f"不支持的交易所: {exchange_name}")
    
    exchange_class = SUPPORTED_EXCHANGES[exchange_name.lower()]
    exchange = exchange_class({
        'apiKey': '',
        'secret': '',
        'timeout': 30000,
        'enableRateLimit': True,
    })
    return exchange

async def get_cached_price(exchange_name: str, symbol: str, max_age_seconds: int = 60):
    """获取缓存的价格数据"""
    cache_key = f"{exchange_name}_{symbol}"
    
    if cache_key in price_cache and cache_key in cache_timestamp:
        age = datetime.now().timestamp() - cache_timestamp[cache_key]
        if age < max_age_seconds:
            return price_cache[cache_key]
    
    return None

async def cache_price(exchange_name: str, symbol: str, data: Dict):
    """缓存价格数据"""
    cache_key = f"{exchange_name}_{symbol}"
    price_cache[cache_key] = data
    cache_timestamp[cache_key] = datetime.now().timestamp()

@server.list_resources()
async def list_resources() -> List[Resource]:
    """列出可用的资源"""
    resources = []
    
    # 添加支持的交易所资源
    for exchange_name in SUPPORTED_EXCHANGES.keys():
        resources.append(
            Resource(
                uri=f"exchange://{exchange_name}",
                name=f"{exchange_name.title()} 交易所",
                description=f"获取 {exchange_name.title()} 交易所的市场信息",
                mimeType="application/json"
            )
        )
    
    # 添加价格缓存资源
    resources.append(
        Resource(
            uri="cache://prices",
            name="价格缓存",
            description="查看当前缓存的价格数据",
            mimeType="application/json"
        )
    )
    
    return resources

@server.read_resource()
async def read_resource(uri: str) -> str:
    """读取指定的资源"""
    if uri.startswith("exchange://"):
        exchange_name = uri.replace("exchange://", "")
        if exchange_name not in SUPPORTED_EXCHANGES:
            raise ValueError(f"不支持的交易所: {exchange_name}")
        
        try:
            exchange = await get_exchange(exchange_name)
            markets = await exchange.load_markets()
            await exchange.close()
            
            # 返回交易所的基本信息
            info = {
                "exchange": exchange_name,
                "total_markets": len(markets),
                "btc_markets": [symbol for symbol in markets.keys() if symbol.startswith('BTC/')],
                "supported_features": {
                    "fetchTicker": exchange.has.get('fetchTicker', False),
                    "fetchOHLCV": exchange.has.get('fetchOHLCV', False),
                    "fetchOrderBook": exchange.has.get('fetchOrderBook', False),
                }
            }
            return json.dumps(info, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"获取交易所信息失败: {str(e)}"
    
    elif uri == "cache://prices":
        cache_info = {
            "cached_items": len(price_cache),
            "cache_data": {}
        }
        
        for key, data in price_cache.items():
            if key in cache_timestamp:
                age = datetime.now().timestamp() - cache_timestamp[key]
                cache_info["cache_data"][key] = {
                    "data": data,
                    "age_seconds": round(age, 2)
                }
        
        return json.dumps(cache_info, ensure_ascii=False, indent=2)
    
    else:
        raise ValueError(f"未知的资源URI: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="get_btc_price",
            description="获取BTC的当前价格",
            inputSchema={
                "type": "object",
                "properties": {
                    "exchange": {
                        "type": "string",
                        "description": "交易所名称",
                        "enum": list(SUPPORTED_EXCHANGES.keys()),
                        "default": "binance"
                    },
                    "symbol": {
                        "type": "string",
                        "description": "交易对符号",
                        "default": "BTC/USDT"
                    }
                },
                "required": []
            }
        ),
        Tool(
            name="get_crypto_price",
            description="获取任意加密货币的当前价格",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "交易对符号，如 BTC/USDT, ETH/USDT"
                    },
                    "exchange": {
                        "type": "string",
                        "description": "交易所名称",
                        "enum": list(SUPPORTED_EXCHANGES.keys()),
                        "default": "binance"
                    }
                },
                "required": ["symbol"]
            }
        ),
        Tool(
            name="get_market_summary",
            description="获取市场概览信息",
            inputSchema={
                "type": "object",
                "properties": {
                    "exchange": {
                        "type": "string",
                        "description": "交易所名称",
                        "enum": list(SUPPORTED_EXCHANGES.keys()),
                        "default": "binance"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回的交易对数量",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 50
                    }
                },
                "required": []
            }
        ),
        Tool(
            name="get_ohlcv",
            description="获取K线数据(OHLCV)",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "交易对符号"
                    },
                    "timeframe": {
                        "type": "string",
                        "description": "时间周期",
                        "enum": ["1m", "5m", "15m", "1h", "4h", "1d"],
                        "default": "1h"
                    },
                    "exchange": {
                        "type": "string",
                        "description": "交易所名称",
                        "enum": list(SUPPORTED_EXCHANGES.keys()),
                        "default": "binance"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回的K线数量",
                        "default": 24,
                        "minimum": 1,
                        "maximum": 1000
                    }
                },
                "required": ["symbol"]
            }
        ),
        Tool(
            name="list_exchanges",
            description="列出所有支持的交易所",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="clear_cache",
            description="清除价格缓存",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """执行工具调用"""
    
    try:
        if name == "get_btc_price":
            exchange_name = arguments.get("exchange", "binance")
            symbol = arguments.get("symbol", "BTC/USDT")
            
            # 检查缓存
            cached_data = await get_cached_price(exchange_name, symbol)
            if cached_data:
                return [TextContent(
                    type="text", 
                    text=f"💰 {symbol} 价格 (来自缓存)\n"
                         f"交易所: {exchange_name.title()}\n"
                         f"当前价格: ${cached_data['price']:,.2f}\n"
                         f"24h变化: {cached_data.get('change', 'N/A')}\n"
                         f"24h成交量: {cached_data.get('volume', 'N/A')}\n"
                         f"更新时间: {cached_data.get('timestamp', 'N/A')}"
                )]
            
            exchange = await get_exchange(exchange_name)
            ticker = await exchange.fetch_ticker(symbol)
            await exchange.close()
            
            # 缓存数据
            cache_data = {
                'price': ticker['last'],
                'change': f"{ticker['percentage']:.2f}%" if ticker['percentage'] else 'N/A',
                'volume': f"{ticker['baseVolume']:,.0f}" if ticker['baseVolume'] else 'N/A',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            await cache_price(exchange_name, symbol, cache_data)
            
            return [TextContent(
                type="text",
                text=f"💰 {symbol} 价格\n"
                     f"交易所: {exchange_name.title()}\n"
                     f"当前价格: ${ticker['last']:,.2f}\n"
                     f"24h变化: {ticker['percentage']:.2f}%" if ticker['percentage'] else "N/A" + "\n"
                     f"24h成交量: {ticker['baseVolume']:,.0f}" if ticker['baseVolume'] else "N/A" + "\n"
                     f"最高价: ${ticker['high']:,.2f}\n"
                     f"最低价: ${ticker['low']:,.2f}\n"
                     f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )]
        
        elif name == "get_crypto_price":
            symbol = arguments["symbol"]
            exchange_name = arguments.get("exchange", "binance")
            
            exchange = await get_exchange(exchange_name)
            ticker = await exchange.fetch_ticker(symbol)
            await exchange.close()
            
            return [TextContent(
                type="text",
                text=f"💎 {symbol} 价格\n"
                     f"交易所: {exchange_name.title()}\n"
                     f"当前价格: ${ticker['last']:,.4f}\n"
                     f"24h变化: {ticker['percentage']:.2f}%" if ticker['percentage'] else "N/A" + "\n"
                     f"24h成交量: {ticker['baseVolume']:,.2f}" if ticker['baseVolume'] else "N/A" + "\n"
                     f"买一价: ${ticker['bid']:,.4f}\n"
                     f"卖一价: ${ticker['ask']:,.4f}"
            )]
        
        elif name == "get_market_summary":
            exchange_name = arguments.get("exchange", "binance")
            limit = arguments.get("limit", 10)
            
            exchange = await get_exchange(exchange_name)
            tickers = await exchange.fetch_tickers()
            await exchange.close()
            
            # 按24h成交量排序
            sorted_tickers = sorted(
                [(symbol, ticker) for symbol, ticker in tickers.items() 
                 if ticker.get('quoteVolume') and 'USDT' in symbol],
                key=lambda x: x[1]['quoteVolume'],
                reverse=True
            )[:limit]
            
            result = f"📊 {exchange_name.title()} 市场概览 (Top {limit})\n\n"
            for i, (symbol, ticker) in enumerate(sorted_tickers, 1):
                change_str = f"{ticker['percentage']:+.2f}%" if ticker['percentage'] else "N/A"
                result += f"{i:2d}. {symbol:<12} ${ticker['last']:>10,.2f} ({change_str})\n"
            
            return [TextContent(type="text", text=result)]
        
        elif name == "get_ohlcv":
            symbol = arguments["symbol"]
            timeframe = arguments.get("timeframe", "1h")
            exchange_name = arguments.get("exchange", "binance")
            limit = arguments.get("limit", 24)
            
            exchange = await get_exchange(exchange_name)
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            await exchange.close()
            
            result = f"📈 {symbol} K线数据 ({timeframe})\n\n"
            result += "时间                开盘价    最高价    最低价    收盘价    成交量\n"
            result += "-" * 70 + "\n"
            
            for candle in ohlcv[-10:]:  # 显示最近10根K线
                timestamp = datetime.fromtimestamp(candle[0] / 1000)
                result += f"{timestamp.strftime('%m-%d %H:%M')} {candle[1]:>8.2f} {candle[2]:>8.2f} {candle[3]:>8.2f} {candle[4]:>8.2f} {candle[5]:>10.0f}\n"
            
            return [TextContent(type="text", text=result)]
        
        elif name == "list_exchanges":
            result = "🏢 支持的交易所:\n\n"
            for i, exchange_name in enumerate(SUPPORTED_EXCHANGES.keys(), 1):
                result += f"{i}. {exchange_name.title()}\n"
            
            return [TextContent(type="text", text=result)]
        
        elif name == "clear_cache":
            cache_count = len(price_cache)
            price_cache.clear()
            cache_timestamp.clear()
            
            return [TextContent(
                type="text",
                text=f"🗑️ 已清除 {cache_count} 个缓存项"
            )]
        
        else:
            raise ValueError(f"未知的工具: {name}")
    
    except Exception as e:
        return [TextContent(
            type="text",
            text=f"❌ 执行工具 '{name}' 时出错: {str(e)}"
        )]

async def main():
    """启动BTC价格MCP服务器"""
    print("🚀 启动BTC价格MCP服务器...")
    print("支持的功能:")
    print("- 实时BTC和加密货币价格")
    print("- 多交易所支持 (Binance, Coinbase, Kraken等)")
    print("- K线数据获取")
    print("- 市场概览")
    print("- 价格缓存机制")
    print("\n等待客户端连接...")
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
