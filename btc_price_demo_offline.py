#!/usr/bin/env python3
"""
BTC价格MCP服务器离线演示
展示MCP服务器的功能和概念，不需要网络连接
"""

import asyncio
import json
from datetime import datetime, timedelta
import random

def generate_mock_btc_price():
    """生成模拟的BTC价格数据"""
    base_price = 95000  # 基础价格
    variation = random.uniform(-0.05, 0.05)  # ±5%的变化
    price = base_price * (1 + variation)
    
    return {
        'symbol': 'BTC/USDT',
        'last': price,
        'high': price * 1.02,
        'low': price * 0.98,
        'percentage': variation * 100,
        'baseVolume': random.uniform(1000, 5000),
        'quoteVolume': price * random.uniform(1000, 5000),
        'bid': price * 0.9999,
        'ask': price * 1.0001,
        'timestamp': datetime.now().isoformat()
    }

def generate_mock_crypto_prices():
    """生成模拟的多种加密货币价格"""
    cryptos = {
        'BTC/USDT': 95000,
        'ETH/USDT': 3500,
        'BNB/USDT': 650,
        'ADA/USDT': 1.2,
        'SOL/USDT': 180,
        'XRP/USDT': 2.1,
        'DOGE/USDT': 0.35,
        'MATIC/USDT': 1.1,
        'DOT/USDT': 8.5,
        'AVAX/USDT': 45
    }
    
    prices = {}
    for symbol, base_price in cryptos.items():
        variation = random.uniform(-0.1, 0.1)  # ±10%的变化
        price = base_price * (1 + variation)
        
        prices[symbol] = {
            'symbol': symbol,
            'last': price,
            'percentage': variation * 100,
            'quoteVolume': price * random.uniform(10000, 100000)
        }
    
    return prices

def generate_mock_ohlcv(symbol, timeframe, limit):
    """生成模拟的K线数据"""
    base_price = 95000 if 'BTC' in symbol else 3500
    ohlcv = []
    
    current_time = datetime.now()
    timeframe_minutes = {'1m': 1, '5m': 5, '15m': 15, '1h': 60, '4h': 240, '1d': 1440}
    interval = timeframe_minutes.get(timeframe, 60)
    
    for i in range(limit):
        timestamp = current_time - timedelta(minutes=interval * (limit - i))
        
        # 生成OHLCV数据
        open_price = base_price * (1 + random.uniform(-0.02, 0.02))
        close_price = open_price * (1 + random.uniform(-0.01, 0.01))
        high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.005))
        low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.005))
        volume = random.uniform(100, 1000)
        
        ohlcv.append([
            int(timestamp.timestamp() * 1000),  # timestamp
            open_price,   # open
            high_price,   # high
            low_price,    # low
            close_price,  # close
            volume        # volume
        ])
        
        base_price = close_price  # 下一根K线的基础价格
    
    return ohlcv

async def demo_mcp_concepts():
    """演示MCP概念"""
    
    print("🎯 BTC价格MCP服务器概念演示")
    print("=" * 60)
    print()
    
    print("📖 什么是MCP?")
    print("-" * 30)
    print("MCP (Model Context Protocol) 是一个开放协议，")
    print("用于连接AI助手和外部数据源。")
    print("我们的BTC价格服务器提供实时加密货币市场数据。")
    print()
    
    print("🏗️ 我们的BTC价格MCP服务器组件:")
    print("-" * 40)
    print("1. 🔧 工具 (Tools):")
    print("   • get_btc_price - 获取BTC当前价格")
    print("   • get_crypto_price - 获取任意加密货币价格")
    print("   • get_market_summary - 获取市场概览")
    print("   • get_ohlcv - 获取K线数据")
    print("   • list_exchanges - 列出支持的交易所")
    print("   • clear_cache - 清除价格缓存")
    print()
    
    print("2. 📚 资源 (Resources):")
    print("   • exchange://{name} - 交易所信息")
    print("   • cache://prices - 价格缓存状态")
    print()
    
    print("3. 🏢 支持的交易所:")
    exchanges = ['Binance', 'Coinbase', 'Kraken', 'Huobi', 'OKX', 'Bybit', 'KuCoin']
    for i, exchange in enumerate(exchanges, 1):
        print(f"   {i}. {exchange}")
    print()

async def demo_tool_execution():
    """演示工具执行"""
    
    print("⚙️ 工具执行演示 (模拟数据):")
    print("-" * 40)
    
    # 演示1: 获取BTC价格
    print("💰 1. 获取BTC价格:")
    btc_data = generate_mock_btc_price()
    print(f"   交易所: Binance")
    print(f"   交易对: {btc_data['symbol']}")
    print(f"   当前价格: ${btc_data['last']:,.2f}")
    print(f"   24h变化: {btc_data['percentage']:+.2f}%")
    print(f"   24h最高: ${btc_data['high']:,.2f}")
    print(f"   24h最低: ${btc_data['low']:,.2f}")
    print(f"   24h成交量: {btc_data['baseVolume']:,.2f} BTC")
    print()
    
    # 演示2: 市场概览
    print("📊 2. 市场概览:")
    crypto_prices = generate_mock_crypto_prices()
    sorted_cryptos = sorted(
        crypto_prices.items(), 
        key=lambda x: x[1]['quoteVolume'], 
        reverse=True
    )[:5]
    
    print("   排名  交易对      价格        24h变化    24h成交量")
    print("   " + "-" * 50)
    for i, (symbol, data) in enumerate(sorted_cryptos, 1):
        change_str = f"{data['percentage']:+.2f}%"
        volume_str = f"${data['quoteVolume']:,.0f}"
        print(f"   {i:2d}.   {symbol:<10} ${data['last']:>8,.2f} {change_str:>8} {volume_str:>12}")
    print()
    
    # 演示3: K线数据
    print("📈 3. BTC K线数据 (1小时):")
    ohlcv_data = generate_mock_ohlcv('BTC/USDT', '1h', 5)
    print("   时间          开盘价    最高价    最低价    收盘价    成交量")
    print("   " + "-" * 60)
    
    for candle in ohlcv_data:
        timestamp = datetime.fromtimestamp(candle[0] / 1000)
        time_str = timestamp.strftime('%m-%d %H:%M')
        print(f"   {time_str} {candle[1]:>8.0f} {candle[2]:>8.0f} {candle[3]:>8.0f} {candle[4]:>8.0f} {candle[5]:>8.1f}")
    print()

async def demo_resource_access():
    """演示资源访问"""
    
    print("📖 资源访问演示:")
    print("-" * 30)
    
    # 演示交易所信息资源
    print("🏢 1. Binance交易所信息:")
    exchange_info = {
        "exchange": "binance",
        "total_markets": 2000,
        "btc_markets": ["BTC/USDT", "BTC/BUSD", "BTC/ETH", "BTC/BNB"],
        "supported_features": {
            "fetchTicker": True,
            "fetchOHLCV": True,
            "fetchOrderBook": True,
            "fetchTrades": True
        }
    }
    print(json.dumps(exchange_info, indent=2, ensure_ascii=False))
    print()
    
    # 演示缓存信息资源
    print("🗂️ 2. 价格缓存状态:")
    cache_info = {
        "cached_items": 3,
        "cache_data": {
            "binance_BTC/USDT": {
                "data": {"price": 95234.56, "change": "+2.34%"},
                "age_seconds": 45.2
            },
            "binance_ETH/USDT": {
                "data": {"price": 3456.78, "change": "-1.23%"},
                "age_seconds": 32.1
            }
        }
    }
    print(json.dumps(cache_info, indent=2, ensure_ascii=False))
    print()

async def demo_usage_examples():
    """演示使用示例"""
    
    print("💡 使用示例:")
    print("-" * 30)
    
    examples = [
        "获取BTC当前价格",
        "比较不同交易所的BTC价格",
        "查看热门加密货币排行榜",
        "分析BTC的1小时K线走势",
        "监控ETH/USDT的价格变化",
        "获取Binance交易所的市场信息"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example}")
    print()
    
    print("🔧 在Claude Desktop中的使用:")
    print("-" * 35)
    print("配置MCP服务器后，你可以直接问Claude:")
    print('• "BTC现在多少钱？"')
    print('• "显示前10名加密货币的价格"')
    print('• "Binance上BTC的24小时走势如何？"')
    print('• "比较Binance和Kraken的BTC价格"')
    print()

async def main():
    """主函数"""
    
    print("🚀 欢迎使用BTC价格MCP服务器演示!")
    print()
    
    await demo_mcp_concepts()
    await demo_tool_execution()
    await demo_resource_access()
    await demo_usage_examples()
    
    print("🎉 演示完成!")
    print("=" * 60)
    print()
    print("📝 接下来的步骤:")
    print("1. 确保网络连接正常")
    print("2. 运行 'py -3.11 btc_price_mcp_server.py' 启动服务器")
    print("3. 在Claude Desktop中配置MCP服务器")
    print("4. 开始询问加密货币价格!")
    print()
    print("📋 Claude Desktop配置示例:")
    print('{')
    print('  "mcpServers": {')
    print('    "btc-price": {')
    print('      "command": "python",')
    print('      "args": ["C:/path/to/btc_price_mcp_server.py"]')
    print('    }')
    print('  }')
    print('}')

if __name__ == "__main__":
    asyncio.run(main())
