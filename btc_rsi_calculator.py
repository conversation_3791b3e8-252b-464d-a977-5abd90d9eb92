#!/usr/bin/env python3
"""
BTC RSI计算器
获取BTC日线数据并计算RSI等技术指标
"""

import asyncio
import ccxt.async_support as ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any

def calculate_rsi(prices: List[float], period: int = 14) -> float:
    """
    计算RSI (Relative Strength Index)
    
    Args:
        prices: 价格列表
        period: RSI周期，默认14
    
    Returns:
        RSI值 (0-100)
    """
    if len(prices) < period + 1:
        return None
    
    # 计算价格变化
    deltas = np.diff(prices)
    
    # 分离上涨和下跌
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    # 计算平均收益和平均损失
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])
    
    # 计算后续的平均值（使用指数移动平均）
    for i in range(period, len(gains)):
        avg_gain = (avg_gain * (period - 1) + gains[i]) / period
        avg_loss = (avg_loss * (period - 1) + losses[i]) / period
    
    # 计算RSI
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return round(rsi, 2)

def calculate_sma(prices: List[float], period: int) -> float:
    """计算简单移动平均线"""
    if len(prices) < period:
        return None
    return round(np.mean(prices[-period:]), 2)

def calculate_ema(prices: List[float], period: int) -> float:
    """计算指数移动平均线"""
    if len(prices) < period:
        return None
    
    multiplier = 2 / (period + 1)
    ema = prices[0]
    
    for price in prices[1:]:
        ema = (price * multiplier) + (ema * (1 - multiplier))
    
    return round(ema, 2)

def calculate_macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
    """计算MACD指标"""
    if len(prices) < slow:
        return None
    
    ema_fast = calculate_ema(prices, fast)
    ema_slow = calculate_ema(prices, slow)
    
    if ema_fast is None or ema_slow is None:
        return None
    
    macd_line = ema_fast - ema_slow
    
    # 简化的信号线计算
    signal_line = macd_line  # 实际应该是MACD线的EMA
    histogram = macd_line - signal_line
    
    return {
        'macd': round(macd_line, 2),
        'signal': round(signal_line, 2),
        'histogram': round(histogram, 2)
    }

def calculate_bollinger_bands(prices: List[float], period: int = 20, std_dev: int = 2) -> Dict:
    """计算布林带"""
    if len(prices) < period:
        return None
    
    sma = calculate_sma(prices, period)
    if sma is None:
        return None
    
    # 计算标准差
    recent_prices = prices[-period:]
    std = np.std(recent_prices)
    
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    
    return {
        'upper': round(upper_band, 2),
        'middle': sma,
        'lower': round(lower_band, 2),
        'width': round(upper_band - lower_band, 2)
    }

async def get_btc_daily_data(exchange_name: str = 'binance', days: int = 100) -> List[Dict]:
    """获取BTC日线数据"""
    
    # 创建交易所实例
    if exchange_name.lower() == 'binance':
        exchange = ccxt.binance()
    elif exchange_name.lower() == 'kraken':
        exchange = ccxt.kraken()
    elif exchange_name.lower() == 'coinbase':
        exchange = ccxt.coinbase()
    else:
        exchange = ccxt.binance()  # 默认使用Binance
    
    exchange.timeout = 30000
    exchange.enableRateLimit = True
    
    try:
        # 获取日线数据
        symbol = 'BTC/USDT' if exchange_name != 'kraken' else 'BTC/USD'
        ohlcv = await exchange.fetch_ohlcv(symbol, '1d', limit=days)
        
        # 转换为更易读的格式
        data = []
        for candle in ohlcv:
            data.append({
                'timestamp': candle[0],
                'date': datetime.fromtimestamp(candle[0] / 1000).strftime('%Y-%m-%d'),
                'open': candle[1],
                'high': candle[2],
                'low': candle[3],
                'close': candle[4],
                'volume': candle[5]
            })
        
        return data
    
    except Exception as e:
        print(f"获取数据失败: {e}")
        return []
    
    finally:
        await exchange.close()

async def analyze_btc_technicals():
    """分析BTC技术指标"""
    
    print("🔍 获取BTC日线数据...")
    
    # 获取数据
    data = await get_btc_daily_data('binance', 100)
    
    if not data:
        print("❌ 无法获取BTC数据")
        return
    
    # 提取收盘价
    close_prices = [candle['close'] for candle in data]
    high_prices = [candle['high'] for candle in data]
    low_prices = [candle['low'] for candle in data]
    volumes = [candle['volume'] for candle in data]
    
    # 获取最新数据
    latest = data[-1]
    current_price = latest['close']
    
    print(f"\n📊 BTC技术分析报告")
    print(f"交易所: Binance")
    print(f"交易对: BTC/USDT")
    print(f"当前价格: ${current_price:,.2f}")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 计算RSI
    rsi_14 = calculate_rsi(close_prices, 14)
    rsi_7 = calculate_rsi(close_prices, 7)
    rsi_21 = calculate_rsi(close_prices, 21)
    
    print(f"\n📈 RSI指标:")
    print(f"RSI(7):  {rsi_7}")
    print(f"RSI(14): {rsi_14}")
    print(f"RSI(21): {rsi_21}")
    
    # RSI解读
    if rsi_14:
        if rsi_14 > 70:
            rsi_signal = "超买区域 🔴"
        elif rsi_14 < 30:
            rsi_signal = "超卖区域 🟢"
        else:
            rsi_signal = "正常区域 🟡"
        print(f"RSI(14)信号: {rsi_signal}")
    
    # 计算移动平均线
    sma_20 = calculate_sma(close_prices, 20)
    sma_50 = calculate_sma(close_prices, 50)
    sma_200 = calculate_sma(close_prices, 200)
    
    ema_12 = calculate_ema(close_prices, 12)
    ema_26 = calculate_ema(close_prices, 26)
    
    print(f"\n📊 移动平均线:")
    print(f"SMA(20):  ${sma_20:,.2f}")
    print(f"SMA(50):  ${sma_50:,.2f}")
    print(f"SMA(200): ${sma_200:,.2f}")
    print(f"EMA(12):  ${ema_12:,.2f}")
    print(f"EMA(26):  ${ema_26:,.2f}")
    
    # 趋势分析
    print(f"\n🎯 趋势分析:")
    if current_price > sma_20:
        print(f"价格 > SMA(20): 短期上涨趋势 ⬆️")
    else:
        print(f"价格 < SMA(20): 短期下跌趋势 ⬇️")
    
    if current_price > sma_50:
        print(f"价格 > SMA(50): 中期上涨趋势 ⬆️")
    else:
        print(f"价格 < SMA(50): 中期下跌趋势 ⬇️")
    
    if current_price > sma_200:
        print(f"价格 > SMA(200): 长期上涨趋势 ⬆️")
    else:
        print(f"价格 < SMA(200): 长期下跌趋势 ⬇️")
    
    # 计算MACD
    macd = calculate_macd(close_prices)
    if macd:
        print(f"\n📉 MACD指标:")
        print(f"MACD线: {macd['macd']}")
        print(f"信号线: {macd['signal']}")
        print(f"柱状图: {macd['histogram']}")
    
    # 计算布林带
    bb = calculate_bollinger_bands(close_prices)
    if bb:
        print(f"\n🎪 布林带:")
        print(f"上轨: ${bb['upper']:,.2f}")
        print(f"中轨: ${bb['middle']:,.2f}")
        print(f"下轨: ${bb['lower']:,.2f}")
        print(f"带宽: ${bb['width']:,.2f}")
        
        # 布林带位置分析
        if current_price > bb['upper']:
            bb_signal = "突破上轨，可能超买 🔴"
        elif current_price < bb['lower']:
            bb_signal = "跌破下轨，可能超卖 🟢"
        else:
            bb_signal = "在布林带内正常波动 🟡"
        print(f"位置: {bb_signal}")
    
    # 成交量分析
    recent_volume = np.mean(volumes[-7:])  # 最近7天平均成交量
    avg_volume = np.mean(volumes[-30:])    # 最近30天平均成交量
    
    print(f"\n📊 成交量分析:")
    print(f"最近7天平均: {recent_volume:,.0f} BTC")
    print(f"最近30天平均: {avg_volume:,.0f} BTC")
    
    if recent_volume > avg_volume * 1.2:
        volume_signal = "成交量放大 📈"
    elif recent_volume < avg_volume * 0.8:
        volume_signal = "成交量萎缩 📉"
    else:
        volume_signal = "成交量正常 📊"
    print(f"成交量状态: {volume_signal}")
    
    # 价格区间分析
    high_52w = max([candle['high'] for candle in data[-365:] if candle])
    low_52w = min([candle['low'] for candle in data[-365:] if candle])
    
    print(f"\n🎯 价格区间:")
    print(f"52周最高: ${high_52w:,.2f}")
    print(f"52周最低: ${low_52w:,.2f}")
    print(f"当前位置: {((current_price - low_52w) / (high_52w - low_52w) * 100):.1f}%")
    
    print(f"\n" + "=" * 60)
    print(f"⚠️  免责声明: 以上分析仅供参考，不构成投资建议")

async def main():
    """主函数"""
    try:
        await analyze_btc_technicals()
    except KeyboardInterrupt:
        print("\n👋 分析被用户中断")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 启动BTC技术分析...")
    print("正在获取实时数据，请稍候...")
    asyncio.run(main())
